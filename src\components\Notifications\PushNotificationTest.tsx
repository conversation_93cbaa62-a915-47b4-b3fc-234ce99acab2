'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useNotificationManager } from '@/hooks/useNotificationManager';
import {
  Bell,
  BellOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Send,
  Wifi,
  WifiOff
} from 'lucide-react';

export function PushNotificationTest() {
  const {
    isSupported,
    permission,
    isSubscribed,
    isLoading,
    error,
    serviceWorkerReady,
    subscribe,
    unsubscribe,
    sendTestNotification
  } = useNotificationManager();

  const [testLoading, setTestLoading] = useState(false);

  const handleTestNotification = async () => {
    setTestLoading(true);
    await sendTestNotification();
    setTestLoading(false);
  };

  const getStatusColor = () => {
    if (!isSupported) return 'text-red-500';
    if (!serviceWorkerReady) return 'text-yellow-500';
    if (isSubscribed) return 'text-green-500';
    return 'text-gray-500';
  };

  const getStatusIcon = () => {
    if (!isSupported) return <XCircle className="h-5 w-5 text-red-500" />;
    if (!serviceWorkerReady) return <Loader2 className="h-5 w-5 text-yellow-500 animate-spin" />;
    if (isSubscribed) return <Bell className="h-5 w-5 text-green-500" />;
    return <BellOff className="h-5 w-5 text-gray-500" />;
  };

  const getOverallStatus = () => {
    if (!isSupported) return 'Not Supported';
    if (!serviceWorkerReady) return 'Initializing...';
    if (permission === 'denied') return 'Permission Denied';
    if (isSubscribed) return 'Active';
    return 'Not Subscribed';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Push Notification System Test
        </CardTitle>
        <CardDescription>
          Test and verify your push notification setup
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Overall Status</div>
            <Badge variant={isSubscribed ? "default" : "secondary"} className={getStatusColor()}>
              {getOverallStatus()}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Browser Support</div>
            <Badge variant={isSupported ? "default" : "destructive"}>
              {isSupported ? (
                <><CheckCircle className="h-3 w-3 mr-1" />Supported</>
              ) : (
                <><XCircle className="h-3 w-3 mr-1" />Not Supported</>
              )}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Permission</div>
            <Badge variant={permission === 'granted' ? "default" : permission === 'denied' ? "destructive" : "secondary"}>
              {permission === 'granted' && <CheckCircle className="h-3 w-3 mr-1" />}
              {permission === 'denied' && <XCircle className="h-3 w-3 mr-1" />}
              {permission === 'default' && <AlertTriangle className="h-3 w-3 mr-1" />}
              {permission.charAt(0).toUpperCase() + permission.slice(1)}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Service Worker</div>
            <Badge variant={serviceWorkerReady ? "default" : "secondary"}>
              {serviceWorkerReady ? (
                <><Wifi className="h-3 w-3 mr-1" />Ready</>
              ) : (
                <><WifiOff className="h-3 w-3 mr-1" />Loading</>
              )}
            </Badge>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Not Supported Warning */}
        {!isSupported && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your browser doesn't support push notifications. Please use a modern browser like Chrome, Firefox, or Safari.
            </AlertDescription>
          </Alert>
        )}

        {/* Permission Denied Help */}
        {permission === 'denied' && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Push notifications are blocked. To enable them:
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Click the lock icon in your browser's address bar</li>
                <li>Change notifications from "Block" to "Allow"</li>
                <li>Refresh this page</li>
              </ol>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        {isSupported && serviceWorkerReady && (
          <div className="flex flex-col sm:flex-row gap-3">
            {!isSubscribed ? (
              <Button
                onClick={subscribe}
                disabled={isLoading || permission === 'denied'}
                className="flex-1"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Bell className="h-4 w-4 mr-2" />
                )}
                Enable Push Notifications
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleTestNotification}
                  disabled={testLoading}
                  className="flex-1"
                >
                  {testLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  Send Test Notification
                </Button>
                
                <Button
                  onClick={unsubscribe}
                  disabled={isLoading}
                  variant="outline"
                  className="flex-1"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <BellOff className="h-4 w-4 mr-2" />
                  )}
                  Disable Notifications
                </Button>
              </>
            )}
          </div>
        )}

        {/* Success Message */}
        {isSubscribed && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              ✅ Push notifications are working! You should receive notifications even when the app is closed.
            </AlertDescription>
          </Alert>
        )}

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Debug Information</h4>
            <div className="space-y-1 text-sm">
              <div><strong>Browser Support:</strong> {isSupported ? 'Yes' : 'No'}</div>
              <div><strong>Permission:</strong> {permission}</div>
              <div><strong>Service Worker Ready:</strong> {serviceWorkerReady ? 'Yes' : 'No'}</div>
              <div><strong>Subscribed:</strong> {isSubscribed ? 'Yes' : 'No'}</div>
              <div><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
              {error && <div><strong>Error:</strong> {error}</div>}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
