'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

interface NotificationSubscription {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  expirationTime?: number | null;
}

interface NotificationManagerState {
  isSupported: boolean;
  permission: NotificationPermission;
  isSubscribed: boolean;
  isLoading: boolean;
  error: string | null;
  subscription: NotificationSubscription | null;
  serviceWorkerReady: boolean;
}

export function useNotificationManager() {
  const { data: session } = useSession();
  const [state, setState] = useState<NotificationManagerState>({
    isSupported: false,
    permission: 'default',
    isSubscribed: false,
    isLoading: false,
    error: null,
    subscription: null,
    serviceWorkerReady: false,
  });

  const registrationRef = useRef<ServiceWorkerRegistration | null>(null);

  // Check browser support
  useEffect(() => {
    const checkSupport = () => {
      const supported = 
        typeof window !== 'undefined' &&
        'serviceWorker' in navigator &&
        'PushManager' in window &&
        'Notification' in window;

      setState(prev => ({
        ...prev,
        isSupported: supported,
        permission: supported ? Notification.permission : 'denied'
      }));

      if (supported) {
        initializeServiceWorker();
      }
    };

    checkSupport();
  }, []);

  // Initialize service worker
  const initializeServiceWorker = useCallback(async () => {
    if (!state.isSupported) return;

    try {
      // Register service worker
      let registration = await navigator.serviceWorker.getRegistration('/');
      
      if (!registration) {
        registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/',
          updateViaCache: 'none'
        });
      }

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;
      registrationRef.current = registration;

      setState(prev => ({ ...prev, serviceWorkerReady: true }));

      // Check existing subscription
      await checkSubscriptionStatus();

      console.log('Service Worker initialized successfully');
    } catch (error) {
      console.error('Service Worker initialization failed:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to initialize service worker',
        serviceWorkerReady: false
      }));
    }
  }, [state.isSupported]);

  // Check current subscription status
  const checkSubscriptionStatus = useCallback(async () => {
    if (!registrationRef.current) return;

    try {
      const subscription = await registrationRef.current.pushManager.getSubscription();
      
      if (subscription) {
        const subscriptionData = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: arrayBufferToBase64(subscription.getKey('p256dh')!),
            auth: arrayBufferToBase64(subscription.getKey('auth')!)
          },
          expirationTime: subscription.expirationTime
        };

        setState(prev => ({
          ...prev,
          isSubscribed: true,
          subscription: subscriptionData
        }));
      } else {
        setState(prev => ({
          ...prev,
          isSubscribed: false,
          subscription: null
        }));
      }
    } catch (error) {
      console.error('Error checking subscription status:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to check subscription status'
      }));
    }
  }, []);

  // Convert ArrayBuffer to Base64
  const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  };

  // Convert Base64 to Uint8Array for VAPID key
  const urlBase64ToUint8Array = (base64String: string): Uint8Array => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  };

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!state.isSupported) {
      throw new Error('Notifications not supported');
    }

    try {
      const permission = await Notification.requestPermission();
      setState(prev => ({ ...prev, permission }));
      return permission;
    } catch (error) {
      console.error('Error requesting permission:', error);
      setState(prev => ({ ...prev, error: 'Failed to request permission' }));
      throw error;
    }
  }, [state.isSupported]);

  // Subscribe to push notifications
  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!session?.user) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    if (!state.isSupported || !registrationRef.current) {
      setState(prev => ({ ...prev, error: 'Push notifications not supported' }));
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Request permission if needed
      if (state.permission !== 'granted') {
        const permission = await requestPermission();
        if (permission !== 'granted') {
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: 'Notification permission denied'
          }));
          return false;
        }
      }

      // Get VAPID public key
      const vapidResponse = await fetch('/api/push/vapid-key');
      if (!vapidResponse.ok) {
        throw new Error('Failed to get VAPID public key');
      }
      const { publicKey } = await vapidResponse.json();

      // Convert VAPID key
      const applicationServerKey = urlBase64ToUint8Array(publicKey);

      // Subscribe to push manager
      const subscription = await registrationRef.current.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey
      });

      // Send subscription to server
      const subscribeResponse = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscription.toJSON())
      });

      if (!subscribeResponse.ok) {
        const errorData = await subscribeResponse.json();
        throw new Error(errorData.error || 'Failed to save subscription');
      }

      // Update state
      const subscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: arrayBufferToBase64(subscription.getKey('auth')!)
        },
        expirationTime: subscription.expirationTime
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        isSubscribed: true,
        subscription: subscriptionData
      }));

      toast.success('Push notifications enabled successfully!');
      console.log('Push notification subscription successful');
      return true;

    } catch (error: any) {
      console.error('Push notification subscription failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Subscription failed'
      }));
      toast.error('Failed to enable push notifications: ' + error.message);
      return false;
    }
  }, [session, state.isSupported, state.permission, requestPermission]);

  // Unsubscribe from push notifications
  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!registrationRef.current) {
      setState(prev => ({ ...prev, error: 'Service worker not ready' }));
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const subscription = await registrationRef.current.pushManager.getSubscription();
      
      if (subscription) {
        await subscription.unsubscribe();
      }

      // Remove from server
      const response = await fetch('/api/push/subscribe', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        console.warn('Failed to remove subscription from server');
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        isSubscribed: false,
        subscription: null
      }));

      toast.success('Push notifications disabled');
      console.log('Push notification unsubscribe successful');
      return true;

    } catch (error: any) {
      console.error('Push notification unsubscribe failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Unsubscribe failed'
      }));
      toast.error('Failed to disable push notifications');
      return false;
    }
  }, []);

  // Send test notification
  const sendTestNotification = useCallback(async (): Promise<boolean> => {
    if (!state.isSubscribed) {
      toast.error('Please enable push notifications first');
      return false;
    }

    try {
      const response = await fetch('/api/push/send/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast.success('Test notification sent!');
        return true;
      } else {
        toast.error(data.message || 'Failed to send test notification');
        return false;
      }
    } catch (error) {
      console.error('Test notification error:', error);
      toast.error('Failed to send test notification');
      return false;
    }
  }, [state.isSubscribed]);

  return {
    ...state,
    subscribe,
    unsubscribe,
    requestPermission,
    sendTestNotification,
    checkSubscriptionStatus
  };
}
