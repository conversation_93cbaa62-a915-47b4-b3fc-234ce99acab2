import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { publisher, subscriber } from './redis';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/options';

let io: SocketIOServer | null = null;

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    name?: string;
    email?: string;
    organizationId?: string | null;
  };
}

export function initializeSocketIO(httpServer: HTTPServer): SocketIOServer {
  if (io) {
    return io;
  }

  io = new SocketIOServer(httpServer, {
    cors: {
      origin: process.env.NODE_ENV === 'production'
        ? process.env.PRODUCTION_DOMAINS?.split(',') || []
        : ['http://localhost:3000', 'http://localhost:4000'],
      methods: ['GET', 'POST'],
      credentials: true,
    },
    transports: ['websocket', 'polling'],
  });

  // Set up Redis adapter for scaling
  io.adapter(createAdapter(publisher, subscriber));

  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization;

      if (!token) {
        return next(new Error('Authentication token required'));
      }

      // Verify session (you might need to adapt this based on your auth setup)
      const session = await getServerSession(authOptions);

      if (!session?.user) {
        return next(new Error('Invalid authentication'));
      }

      socket.userId = session.user.id;
      socket.user = {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        organizationId: session.user.organizationId
      };
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  // Connection handling
  io.on('connection', (socket: AuthenticatedSocket) => {
    if (!socket.user) return;

    // Join user-specific room
    socket.join(`user:${socket.user.id}`);

    // Join organization room if available
    if (socket.user.organizationId) {
      socket.join(`org:${socket.user.organizationId}`);
    }

    socket.on('disconnect', () => {
      // Handle disconnect
    });
  });

  return io;
}

export function getSocketIOServer(): SocketIOServer | null {
  return io;
}

// Broadcast functions
export function broadcastToUser(userId: string, event: string, data: any): void {
  if (!io) return;
  io.to(`user:${userId}`).emit(event, data);
}

export function broadcastToOrganization(orgId: string, event: string, data: any): void {
  if (!io) return;
  io.to(`org:${orgId}`).emit(event, data);
}

// Redis pub/sub for notifications
export function publishNotification(channel: string, data: any): void {
  publisher.publish(channel, JSON.stringify(data));
}

// Subscribe to notification channels
export function subscribeToNotifications(): void {
  subscriber.subscribe('notifications');

  subscriber.on('message', async (channel, message) => {
    if (channel === 'notifications' && io) {
      try {
        const data = JSON.parse(message);

        if (data.userId) {
          // Check if user is connected via Socket.IO
          const userSockets = io.sockets.adapter.rooms.get(`user:${data.userId}`);
          const isUserOnline = userSockets && userSockets.size > 0;

          console.log(`User ${data.userId} online status: ${isUserOnline ? 'online' : 'offline'}`);

          if (isUserOnline) {
            // User is online - send via Socket.IO
            console.log(`Sending notification via Socket.IO to user: ${data.userId}`);
            broadcastToUser(data.userId, 'notification', data);
          } else {
            // User is offline - send via Web Push
            console.log(`Sending notification via Web Push to user: ${data.userId}`);
            try {
              const { PushNotificationService } = await import('@/services/PushNotification.service');

              if (PushNotificationService.isConfigured() && data.notification) {
                const pushSuccess = await PushNotificationService.sendToUser(data.userId, {
                  title: data.notification.title || 'New Notification',
                  body: data.notification.description || 'You have a new notification',
                  icon: '/icons/icon-192x192.png',
                  badge: '/icons/badge-72x72.png',
                  data: {
                    url: data.notification.link || '/notifications',
                    notificationId: data.notification._id,
                  },
                  tag: data.notification._id || 'notification',
                  requireInteraction: false,
                });

                if (!pushSuccess) {
                  console.log(`Push notification failed for user ${data.userId}, user may not have a valid subscription`);
                }
              } else {
                console.log('Push notifications not configured or notification data missing');
              }
            } catch (pushError) {
              console.error('Failed to send push notification:', pushError);
            }
          }
        }

        if (data.organizationId) {
          // For organization notifications, send to all online users via Socket.IO
          // and offline users via Web Push
          broadcastToOrganization(data.organizationId, 'notification', data);
        }
      } catch (error) {
        console.error('Error processing notification message:', error);
      }
    }
  });
}

// Initialize the notification system (call this once)
export function initializeNotificationSystem(): void {
  if (typeof window !== 'undefined') return; // Only run on server

  try {
    subscribeToNotifications();
    console.log('Notification system initialized');
  } catch (error) {
    console.error('Failed to initialize notification system:', error);
  }
}
