import Redis from 'ioredis';

// Main Redis instance for caching
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: 3,
  lazyConnect: false, // Connect immediately
  enableReadyCheck: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
});

// Publisher instance
const publisher = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
});

// Subscriber instance
const subscriber = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
});

// Initialize connections
redis.on('connect', () => {
  console.log('Redis connected successfully');
});

redis.on('error', (err) => {
  console.error('Redis connection error:', err);
});

redis.on('ready', () => {
  console.log('Redis is ready to accept commands');
});

// Helper functions for common operations
export const RedisHelper = {
  // Generate cache key with consistent format
  generateKey: (prefix: string, ...parts: string[]) => {
    return `${prefix}:${parts.join(':')}`;
  },

  // Set with automatic JSON serialization
  setJSON: async (key: string, data: any, ttl?: number) => {
    const serialized = JSON.stringify(data);
    if (ttl) {
      return await redis.setex(key, ttl, serialized);
    }
    return await redis.set(key, serialized);
  },

  // Get with automatic JSON deserialization
  getJSON: async <T = any>(key: string): Promise<T | null> => {
    const data = await redis.get(key);
    if (!data) return null;
    try {
      return JSON.parse(data) as T;
    } catch (error) {
      console.error('Redis JSON parse error:', error);
      return null;
    }
  },

  // Delete multiple keys by pattern
  deleteByPattern: async (pattern: string) => {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      return await redis.del(...keys);
    }
    return 0;
  },

  // Check if Redis is connected
  isConnected: () => {
    return redis.status === 'ready';
  },
};

// Search caching functionality
export class SearchCache {
  private static readonly CACHE_PREFIX = 'search:';
  private static readonly CACHE_TTL = 300; // 5 minutes

  static generateCacheKey(organizationId: string, query: string, filters: any): string {
    const filterString = JSON.stringify(filters);
    return `${this.CACHE_PREFIX}${organizationId}:${query}:${Buffer.from(filterString).toString('base64')}`;
  }

  static async get(cacheKey: string): Promise<any | null> {
    if (!RedisHelper.isConnected()) return null;

    try {
      const cached = await redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  static async set(cacheKey: string, data: any): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(data));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  static async invalidateUserCache(organizationId: string): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      const pattern = `${this.CACHE_PREFIX}${organizationId}:*`;
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Redis invalidation error:', error);
    }
  }

  static async getPopularSearches(organizationId: string, limit: number = 10): Promise<string[]> {
    if (!RedisHelper.isConnected()) return [];

    try {
      const key = `popular_searches:${organizationId}`;
      const searches = await redis.zrevrange(key, 0, limit - 1);
      return searches;
    } catch (error) {
      console.error('Redis popular searches error:', error);
      return [];
    }
  }

  static async trackSearch(organizationId: string, query: string): Promise<void> {
    if (!RedisHelper.isConnected()) return;

    try {
      const key = `popular_searches:${organizationId}`;
      await redis.zincrby(key, 1, query);
      await redis.expire(key, 86400 * 7); // 7 days
    } catch (error) {
      console.error('Redis track search error:', error);
    }
  }
}

export { redis as default, publisher, subscriber };
