# Notification System Setup Guide

This guide covers the complete setup of the TaskMantra notification system, including Socket.io real-time notifications, browser push notifications, and Redis pub/sub integration.

## Overview

The notification system consists of:
1. **Socket.io** - Real-time bidirectional communication
2. **Redis Pub/Sub** - Message broadcasting across server instances
3. **Browser Push Notifications** - Notifications when app is not active
4. **In-app Notifications** - Real-time UI updates

## Prerequisites

- Node.js 18+ 
- Redis server running
- MongoDB database
- HTTPS domain (required for push notifications in production)

## Environment Variables

Add these variables to your `.env.local` file:

```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379
# or for Redis Cloud/external service:
# REDIS_URL=redis://username:password@host:port

# Push Notification VAPID Keys (generate using web-push library)
VAPID_PUBLIC_KEY=your_vapid_public_key_here
VAPID_PRIVATE_KEY=your_vapid_private_key_here
VAPID_EMAIL=mailto:<EMAIL>

# Socket.io Configuration (optional)
NEXT_PUBLIC_SOCKET_URL=http://localhost:4000
# In production, set to your domain:
# NEXT_PUBLIC_SOCKET_URL=https://yourdomain.com

# Production domains for CORS (comma-separated)
PRODUCTION_DOMAINS=https://yourdomain.com,https://www.yourdomain.com
```

## Generating VAPID Keys

VAPID keys are required for browser push notifications. Generate them using the web-push library:

```bash
# Install web-push globally
npm install -g web-push

# Generate VAPID keys
web-push generate-vapid-keys

# Output will be:
# Public Key: BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U
# Private Key: tUxbwoiH7VlJrfawqDQUSEEanh9hL5iTnLW1pOXgecg
```

Add these keys to your environment variables.

## Redis Setup

### Local Redis (Development)
```bash
# Install Redis (macOS)
brew install redis

# Install Redis (Ubuntu)
sudo apt-get install redis-server

# Start Redis
redis-server

# Test Redis connection
redis-cli ping
# Should return: PONG
```

### Redis Cloud (Production)
1. Sign up for Redis Cloud or use your preferred Redis provider
2. Create a new database
3. Copy the connection URL to `REDIS_URL` environment variable

## Testing the System

### 1. Test Socket.io Connection
1. Start your development server: `npm run dev`
2. Open the application in your browser
3. Check the user profile component - you should see a green wifi icon indicating connection
4. Open browser console and look for "Connected to Socket.IO server" message

### 2. Test Real-time Notifications
1. Navigate to `/test-notification`
2. Fill in the notification details
3. Click "Send Test Notification"
4. You should see the notification appear in the notifications popover immediately

### 3. Test Push Notifications
1. Ensure you have granted notification permission in your browser
2. Subscribe to push notifications (this happens automatically when using the PWA features)
3. Navigate to `/test-notification`
4. Click "Send Push Notification"
5. You should receive a browser push notification

### 4. Test Background Push Notifications
1. Subscribe to push notifications
2. Minimize or close the browser tab
3. Use the test endpoint or create a notification via API
4. You should receive a push notification even with the app closed

## API Endpoints

### Push Notification Endpoints
- `GET /api/push/vapid-key` - Get VAPID public key for subscription
- `POST /api/push/subscribe` - Subscribe user to push notifications
- `DELETE /api/push/subscribe` - Unsubscribe user from push notifications
- `POST /api/push/send` - Send push notification (testing)
- `GET /api/push/send` - Check push notification configuration

### Notification Endpoints
- `GET /api/notifications` - Get user notifications
- `POST /api/notifications` - Create new notification
- `POST /api/test-notification` - Create test notification

## Troubleshooting

### Socket.io Connection Issues
- Check if Redis is running: `redis-cli ping`
- Verify `REDIS_URL` environment variable
- Check browser console for connection errors
- Ensure no firewall blocking WebSocket connections

### Push Notification Issues
- Verify VAPID keys are correctly set
- Check notification permission in browser settings
- Ensure HTTPS in production (required for push notifications)
- Check browser console for subscription errors

### Redis Connection Issues
- Verify Redis server is running
- Check `REDIS_URL` format
- Test Redis connection: `redis-cli -u $REDIS_URL ping`
- Check firewall/network connectivity

### Common Error Messages

**"Push notifications not configured"**
- Missing VAPID environment variables
- Check `VAPID_PUBLIC_KEY`, `VAPID_PRIVATE_KEY`, and `VAPID_EMAIL`

**"Failed to connect to Redis"**
- Redis server not running
- Incorrect `REDIS_URL`
- Network connectivity issues

**"WebSocket connection failed"**
- CORS configuration issue
- Check `PRODUCTION_DOMAINS` in production
- Firewall blocking WebSocket connections

## Production Deployment

### 1. Environment Setup
- Set all required environment variables
- Use Redis Cloud or managed Redis service
- Ensure HTTPS is enabled (required for push notifications)

### 2. CORS Configuration
- Add your production domains to `PRODUCTION_DOMAINS`
- Update Socket.io CORS settings in `src/lib/socketio.ts`

### 3. Service Worker
- Ensure service worker is properly registered
- Test push notifications on production domain
- Verify VAPID keys are working

## Monitoring

### Health Checks
- `GET /api/health/redis` - Check Redis connection
- `GET /api/health/socketio` - Check Socket.io status

### Logs to Monitor
- Socket.io connection/disconnection events
- Redis pub/sub message processing
- Push notification delivery status
- Notification creation and delivery

## Security Considerations

1. **VAPID Keys**: Keep private key secure, never expose in client-side code
2. **Redis**: Use authentication in production
3. **CORS**: Restrict to your domains only
4. **Rate Limiting**: Implement rate limiting for notification endpoints
5. **User Permissions**: Verify user permissions before sending notifications

## Performance Optimization

1. **Redis Connection Pooling**: Use connection pooling for high traffic
2. **Notification Batching**: Batch notifications for multiple users
3. **Push Notification Cleanup**: Remove invalid subscriptions automatically
4. **Socket.io Scaling**: Use Redis adapter for multiple server instances

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review browser console and server logs
3. Test individual components using the test endpoints
4. Verify environment variables are correctly set
