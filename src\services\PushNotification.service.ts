import webpush from 'web-push';
import { User } from '@/models/User';
import { connectDB } from '@/Utility/db';

// Configure web-push with VAPID keys
if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
  webpush.setVapidDetails(
    'mailto:' + (process.env.VAPID_EMAIL || '<EMAIL>'),
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
}

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  tag?: string;
  renotify?: boolean;
  silent?: boolean;
  requireInteraction?: boolean;
}

export class PushNotificationService {
  /**
   * Send push notification to a specific user with retry mechanism
   */
  static async sendToUser(userId: string, payload: PushNotificationPayload, retries: number = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await connectDB();

        const user = await User.findById(userId).select('pushSubscription');

        if (!user || !user.pushSubscription) {
          console.log(`No push subscription found for user: ${userId}`);
          return false;
        }

        // Validate that the subscription has required fields
        if (!user.pushSubscription.endpoint || !user.pushSubscription.keys) {
          console.log(`Invalid push subscription for user ${userId}: missing endpoint or keys`);
          // Remove the invalid subscription
          await User.findByIdAndUpdate(userId, {
            $unset: { pushSubscription: 1 }
          });
          console.log(`Removed invalid push subscription for user: ${userId}`);
          return false;
        }

        // Validate that keys have required fields
        if (!user.pushSubscription.keys.p256dh || !user.pushSubscription.keys.auth) {
          console.log(`Invalid push subscription keys for user ${userId}: missing p256dh or auth`);
          // Remove the invalid subscription
          await User.findByIdAndUpdate(userId, {
            $unset: { pushSubscription: 1 }
          });
          console.log(`Removed invalid push subscription for user: ${userId}`);
          return false;
        }

        const subscription = {
          endpoint: user.pushSubscription.endpoint,
          keys: user.pushSubscription.keys,
        };

        const notificationPayload = JSON.stringify({
          title: payload.title,
          body: payload.body,
          icon: payload.icon || '/icons/icon-192x192.png',
          badge: payload.badge || '/icons/badge-72x72.png',
          data: {
            url: payload.data?.url || '/notifications',
            ...payload.data,
          },
          actions: payload.actions || [
            {
              action: 'view',
              title: 'View',
            },
            {
              action: 'dismiss',
              title: 'Dismiss',
            },
          ],
          tag: payload.tag || 'notification',
          renotify: payload.renotify || false,
          silent: payload.silent || false,
          requireInteraction: payload.requireInteraction || false,
        });

        await webpush.sendNotification(subscription, notificationPayload);
        console.log(`Push notification sent to user: ${userId} (attempt ${attempt})`);
        return true;
      } catch (error: any) {
        console.error(`Failed to send push notification to user ${userId} (attempt ${attempt}):`, error);

        // If the subscription is invalid, remove it from the user and don't retry
        if (error.statusCode === 410 || error.statusCode === 404) {
          try {
            await User.findByIdAndUpdate(userId, {
              $unset: { pushSubscription: 1 }
            });
            console.log(`Removed invalid push subscription for user: ${userId}`);
          } catch (updateError) {
            console.error(`Failed to remove invalid subscription for user ${userId}:`, updateError);
          }
          return false; // Don't retry for invalid subscriptions
        }

        // For network errors or temporary failures, retry with exponential backoff
        if (attempt < retries && (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || error.statusCode >= 500)) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
          console.log(`Retrying push notification for user ${userId} in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        // If all retries failed or it's a non-retryable error
        if (attempt === retries) {
          console.error(`All retry attempts failed for user ${userId}`);
        }
      }
    }

    return false;
  }

  /**
   * Send push notification to multiple users
   */
  static async sendToUsers(userIds: string[], payload: PushNotificationPayload): Promise<{
    successful: number;
    failed: number;
    results: Array<{ userId: string; success: boolean; error?: string }>;
  }> {
    const results = await Promise.allSettled(
      userIds.map(async (userId) => {
        const success = await this.sendToUser(userId, payload);
        return { userId, success };
      })
    );

    const processedResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          userId: userIds[index],
          success: false,
          error: result.reason?.message || 'Unknown error',
        };
      }
    });

    const successful = processedResults.filter(r => r.success).length;
    const failed = processedResults.length - successful;

    return {
      successful,
      failed,
      results: processedResults,
    };
  }

  /**
   * Send push notification to all users with active subscriptions
   */
  static async sendToAllSubscribed(payload: PushNotificationPayload): Promise<{
    successful: number;
    failed: number;
  }> {
    try {
      await connectDB();

      const users = await User.find(
        {
          'pushSubscription.endpoint': { $exists: true, $ne: null },
          'pushSubscription.keys.p256dh': { $exists: true, $ne: null },
          'pushSubscription.keys.auth': { $exists: true, $ne: null }
        },
        { _id: 1 }
      );

      if (users.length === 0) {
        console.log('No users with push subscriptions found');
        return { successful: 0, failed: 0 };
      }

      const userIds = users.map(user => user._id.toString());
      const result = await this.sendToUsers(userIds, payload);

      return {
        successful: result.successful,
        failed: result.failed,
      };
    } catch (error: any) {
      console.error('Failed to send push notifications to all users:', error);
      return { successful: 0, failed: 1 };
    }
  }

  /**
   * Check if push notifications are properly configured
   */
  static isConfigured(): boolean {
    return !!(
      process.env.VAPID_PUBLIC_KEY &&
      process.env.VAPID_PRIVATE_KEY &&
      process.env.VAPID_EMAIL
    );
  }

  /**
   * Get push notification configuration status
   */
  static getConfigStatus(): {
    configured: boolean;
    missingKeys: string[];
  } {
    const requiredKeys = ['VAPID_PUBLIC_KEY', 'VAPID_PRIVATE_KEY', 'VAPID_EMAIL'];
    const missingKeys = requiredKeys.filter(key => !process.env[key]);

    return {
      configured: missingKeys.length === 0,
      missingKeys,
    };
  }

  /**
   * Clean up invalid push subscriptions from the database
   */
  static async cleanupInvalidSubscriptions(): Promise<{
    cleaned: number;
    errors: number;
  }> {
    try {
      await connectDB();

      // Find users with invalid subscriptions
      const usersWithInvalidSubscriptions = await User.find({
        $or: [
          { 'pushSubscription.endpoint': { $exists: false } },
          { 'pushSubscription.endpoint': null },
          { 'pushSubscription.endpoint': '' },
          { 'pushSubscription.keys': { $exists: false } },
          { 'pushSubscription.keys': null },
          { 'pushSubscription.keys.p256dh': { $exists: false } },
          { 'pushSubscription.keys.p256dh': null },
          { 'pushSubscription.keys.p256dh': '' },
          { 'pushSubscription.keys.auth': { $exists: false } },
          { 'pushSubscription.keys.auth': null },
          { 'pushSubscription.keys.auth': '' },
        ]
      }, { _id: 1 });

      if (usersWithInvalidSubscriptions.length === 0) {
        console.log('No invalid push subscriptions found');
        return { cleaned: 0, errors: 0 };
      }

      // Remove invalid subscriptions
      const result = await User.updateMany(
        {
          _id: { $in: usersWithInvalidSubscriptions.map(u => u._id) }
        },
        {
          $unset: { pushSubscription: 1 }
        }
      );

      console.log(`Cleaned up ${result.modifiedCount} invalid push subscriptions`);
      return { cleaned: result.modifiedCount, errors: 0 };
    } catch (error: any) {
      console.error('Failed to cleanup invalid subscriptions:', error);
      return { cleaned: 0, errors: 1 };
    }
  }

  /**
   * Clean up expired push subscriptions
   */
  static async cleanupExpiredSubscriptions(): Promise<{
    cleaned: number;
    errors: number;
  }> {
    try {
      await connectDB();

      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Find subscriptions that have expired or are very old
      const expiredUsers = await User.find({
        $or: [
          { 'pushSubscription.expirationTime': { $lt: now } },
          { 'pushSubscription.subscribedAt': { $lt: thirtyDaysAgo } }
        ]
      }, { _id: 1, pushSubscription: 1 });

      let cleaned = 0;
      let errors = 0;

      for (const user of expiredUsers) {
        try {
          // Test if subscription is still valid by attempting to send a test notification
          const testResult = await this.testSubscription(user._id.toString());

          if (!testResult) {
            await User.findByIdAndUpdate(user._id, {
              $unset: { pushSubscription: 1 }
            });
            cleaned++;
            console.log(`Removed expired subscription for user: ${user._id}`);
          }
        } catch (error) {
          console.error(`Error testing subscription for user ${user._id}:`, error);
          errors++;
        }
      }

      return { cleaned, errors };
    } catch (error: any) {
      console.error('Failed to cleanup expired subscriptions:', error);
      return { cleaned: 0, errors: 1 };
    }
  }

  /**
   * Test if a subscription is still valid without sending a visible notification
   */
  static async testSubscription(userId: string): Promise<boolean> {
    try {
      await connectDB();

      const user = await User.findById(userId).select('pushSubscription');
      if (!user?.pushSubscription) return false;

      const subscription = {
        endpoint: user.pushSubscription.endpoint,
        keys: user.pushSubscription.keys,
      };

      // Send a silent test notification
      const testPayload = JSON.stringify({
        title: 'Test',
        body: 'Test',
        silent: true,
        tag: 'test-notification'
      });

      await webpush.sendNotification(subscription, testPayload);
      return true;
    } catch (error: any) {
      // If subscription is invalid, webpush will throw an error
      if (error.statusCode === 410 || error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Get subscription statistics
   */
  static async getSubscriptionStats(): Promise<{
    total: number;
    active: number;
    invalid: number;
    expired: number;
  }> {
    try {
      await connectDB();

      const total = await User.countDocuments({
        'pushSubscription.endpoint': { $exists: true }
      });

      const invalid = await User.countDocuments({
        $or: [
          { 'pushSubscription.endpoint': null },
          { 'pushSubscription.endpoint': '' },
          { 'pushSubscription.keys': null },
          { 'pushSubscription.keys.p256dh': null },
          { 'pushSubscription.keys.auth': null }
        ]
      });

      const now = new Date();
      const expired = await User.countDocuments({
        'pushSubscription.expirationTime': { $lt: now }
      });

      const active = total - invalid - expired;

      return { total, active, invalid, expired };
    } catch (error: any) {
      console.error('Failed to get subscription stats:', error);
      return { total: 0, active: 0, invalid: 0, expired: 0 };
    }
  }

  /**
   * Test connectivity to push service
   */
  static async testPushServiceConnectivity(): Promise<{
    configured: boolean;
    reachable: boolean;
    error?: string;
  }> {
    try {
      if (!this.isConfigured()) {
        return { configured: false, reachable: false, error: 'VAPID keys not configured' };
      }

      // Create a dummy subscription to test connectivity
      const testEndpoint = 'https://fcm.googleapis.com/fcm/send/test';
      const testSubscription = {
        endpoint: testEndpoint,
        keys: {
          p256dh: 'test-key',
          auth: 'test-auth'
        }
      };

      const testPayload = JSON.stringify({
        title: 'Test',
        body: 'Connectivity test',
        silent: true
      });

      try {
        await webpush.sendNotification(testSubscription, testPayload);
        return { configured: true, reachable: true };
      } catch (error: any) {
        // Expected to fail with test data, but should not be a network error
        if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
          return { configured: true, reachable: false, error: 'Network connectivity issue' };
        }
        return { configured: true, reachable: true }; // Other errors are expected with test data
      }
    } catch (error: any) {
      return { configured: false, reachable: false, error: error.message };
    }
  }

  /**
   * Get last cleanup time from cache/storage
   */
  static async getLastCleanupTime(): Promise<Date | null> {
    // This could be stored in Redis or database
    // For now, return null - implement based on your caching strategy
    return null;

  }

  /**
   * Get cleanup recommendations
   */
  static async getCleanupRecommendations(): Promise<string[]> {
    try {
      const stats = await this.getSubscriptionStats();
      const recommendations: string[] = [];

      if (stats.invalid > 0) {
        recommendations.push(`${stats.invalid} invalid subscriptions should be cleaned up`);
      }

      if (stats.expired > 0) {
        recommendations.push(`${stats.expired} expired subscriptions should be removed`);
      }

      if (stats.total > 1000) {
        recommendations.push('Consider implementing automatic cleanup for large subscription counts');
      }

      if (recommendations.length === 0) {
        recommendations.push('No cleanup needed - all subscriptions appear healthy');
      }

      return recommendations;
    } catch (error) {
      return ['Unable to generate recommendations due to error'];
    }
  }
}
